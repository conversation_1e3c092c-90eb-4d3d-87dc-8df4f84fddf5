from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from mc2.cashflow.payment_intents.vendors.stripe import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mc2.cashflow.payment_intents.vendors.satispay import SatispayVendor
from mc2.cashflow.models import CcpPaymentIntents as PaymentIntent
from .utils import init_vendor, handle_payment_succeeded, handle_payment_canceled

import logging
logger = logging.getLogger(__name__)


class GenericWebhookView(APIView):
    permission_classes = []
    caller = None
    vendor_class = None
    @method_decorator(csrf_exempt)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def post(self, request, *args, **kwargs):
        logger.info("Webhook POST received: path=%s, caller=%s", request.path, self.caller)
        

        if not self.caller:
            return HttpResponse(status=400)

        try:
            payment_status = self.vendor_class.handle_webhook(request)
            logger.info("Webhook POST payment_status: path=%s, caller=%s, payment_status=%s", request.path, self.caller, payment_status)
            if payment_status == 'succeeded':
                self.handle_succeeded()
            return HttpResponse(status=200)
        except Exception as e:
            logger.error("Webhook POST error: path=%s, caller=%s, error=%s", request.path, self.caller, e)
            pass

        return HttpResponse(status=400)


    def get(self, request, *args, **kwargs):
        pass


    def handle_succeeded(self):
        return handle_payment_succeeded(self.vendor_class.transaction_id, payment_method=self.vendor_class.payment_method_id, publish=self.vendor_class.publish, lock=self.vendor_class.lock)

    def handle_canceled(self):
        return handle_payment_canceled(self.vendor_class.transaction_id, payment_method=self.vendor_class.payment_method_id, publish=self.vendor_class.publish)

class StripeReceiverView(GenericWebhookView):
    caller = 'STRIPE'
    def __init__(self):
        self.vendor_class = init_vendor({'nome': self.caller})




class SatispayReceiverView(GenericWebhookView):
    caller = 'SATISPAY'
    env = 'PRODUCTION'

    def get(self, request, *args, **kwargs):
        try:
            transaction_id = self.request.GET['transaction_id']
            intent = PaymentIntent.objects.filter(token=transaction_id).first()
            config = {
                'key_id': intent.channel_key,
                'private_key': intent.satispay_private_key,
                'env': self.env
            }
        except Exception as e:
            return HttpResponse(status=400)

        self.vendor_class = init_vendor({'nome': self.caller, 'config': config})

        payment_status = self.vendor_class.get_payment_status(transaction_id)

        if payment_status == 'ACCEPTED':
            self.handle_succeeded()
            return HttpResponse(status=200)

        if payment_status == 'CANCELED':
            self.handle_canceled()
            return HttpResponse(status=200)

        if payment_status == 'PENDING':
            return HttpResponse(status=200)

        print('payment_status', payment_status)
        return HttpResponse(status=400)
    # def __init__(self, *args, **kwargs):
    #     intent = PaymentIntent.objects.get(token=self.token)
    #     self.vendor_class = init_vendor({'nome': self.caller, 'config': {}})