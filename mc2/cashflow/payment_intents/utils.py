from mc2.cashflow.models import CcpMovement, CcpType, CcpPaymentIntents, CoreBankAccount, CcpPaymentMethod
from mc2.common.models import Parameter
from mc2.cashflow.movements.utils import create as create_movement
from mc2.cashflow.payments.utils import create as create_payment
from datetime import datetime
import time
from datetime import timezone
from .vendors.stripe import StripeVendor
from .vendors.satispay import SatispayVendor
from django.conf import settings
from django.db import transaction


def init_vendor(vendor):

    if vendor['nome'].upper() == 'STRIPE':
        stripe_keys = Parameter.objects.filter(name__in=['STRIPE_DEFAULT_PUBLISH', 'STRIPE_PUBLIC_KEY', 'STRIPE_SECRET_KEY']).order_by('name').all()
        public_key = stripe_keys[1].value
        secret_key = stripe_keys[2].value
        publish = stripe_keys[0].value == 't'
        lock = True
        return StripeVendor(public_key=public_key, secret_key=secret_key, publish=publish, lock=lock)

    if vendor['nome'].upper() == 'SATISPAY':
        satispay_keys = Parameter.objects.filter(name__in=['SATISPAY_DEFAULT_LOCK', 'SATISPAY_DEFAULT_PUBLISH', 'SATISPAY_PAYMENT_ID']).order_by('name').all()
        lock = satispay_keys[0].value == 't'
        publish = satispay_keys[1].value == 't'
        payment_method_id = satispay_keys[2].value
        return SatispayVendor(
            env=vendor['config']['env'],
            now=datetime.now(timezone.utc),
            key_id=vendor['config']['key_id'],
            private_key=vendor['config']['private_key'],
            payment_method_id=payment_method_id,
            publish=publish,
            mastercom_url=settings.MASTERCOM_URL,
            lock=lock
            )

    return None

# Restituisce la banca di default per i pagamenti online oppure quella eventualmente associata all'utente
def init_bank(request):
    id_banca = request.data.get('banca', None)
    banca = None
    if not id_banca:
        try:
            banca = CoreBankAccount.objects.get(online_payment_default=True)
        except:
            pass
    else:
        try:
            banca = CoreBankAccount.objects.get(id=id_banca)
        except:
            pass

    return banca

def init_bank_json(data):
    id_banca = data.get('banca', None)
    banca = None
    if not id_banca:
        try:
            banca = CoreBankAccount.objects.get(online_payment_default=True)
        except:
            pass
    else:
        try:
            banca = CoreBankAccount.objects.get(id=id_banca)
        except:
            pass

    return banca

# Crea una lista di payment_intents a partire dai dati ricevuti
def     crea_payment_intents(data, parente, studente, anno, *args, **kwargs):
    payer_type = 'P'
    payment_intents = []
    bank = kwargs.get('banca', None)
    request = kwargs.get('request', None)
    channel_intents_data = kwargs.get('channel_intents_data', [])
    channel = request.get('canale', None)

    subject_data = request.get('cognome_nome_studente', None)
    subject_class = request.get('classe_studente', None)
    subject_school_address = request.get('indirizzo_studente', None)
    subject_school_address_code = request.get('codice_indirizzo_studente', None)

    payer_surname = request.get('cognome_parente', None)
    payer_name = request.get('nome_parente', None)
    payer_fiscal_code = request.get('codice_fiscale_parente', None)
    payer_address = request.get('indirizzo_parente', None)
    payer_city = request.get('citta_parente', None)
    payer_province = request.get('provincia_parente', None)
    payer_zip_code = request.get('cap_parente', None)


    date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    articoli_qta = []
    try:
        for articolo in data:
            if articolo['tipo'] == 'tipo_movimento' and articolo['qta'] > 1:
                # append to data the same item for qta - 1 times
                for i in range(1, articolo['qta']):
                    articoli_qta.append(articolo)
    except Exception as e:
        print('Errore in articoli_qta', e)
        pass

    data.extend(articoli_qta)
    for articolo in data:
        intent = CcpPaymentIntents()

        intent.payer_type = payer_type
        intent.payer_id = parente
        intent.subject_id = studente
        intent.bank_account_id = bank.id
        intent.payment_channel = channel['nome']
        intent.date_created = date
        intent.subject_data = subject_data
        intent.subject_class = subject_class
        intent.subject_school_address = subject_school_address
        intent.subject_school_address_code = subject_school_address_code
        intent.payer_surname = payer_surname
        intent.payer_name = payer_name
        intent.payer_fiscal_code = payer_fiscal_code
        intent.payer_address = payer_address
        intent.payer_city = payer_city
        intent.payer_province = payer_province
        intent.payer_zip_code = payer_zip_code


        if articolo['tipo'] == 'movimento':
            m = CcpMovement.objects.get(id=articolo['id'])
            intent.amount = round(m.unsolved(), 2)
            intent.payment_object_type = 'movement'
            intent.payment_object_id = m.id
            intent.school_year = m.school_year
        elif articolo['tipo'] == 'tipo_movimento':
            tm = CcpType.objects.get(id=articolo['id'])

            intent.school_year = anno.replace('_', '/')
            intent.payment_object_type = 'movement_type'
            intent.payment_object_id = tm.id
            if articolo['totale']:
                intent.amount = round(articolo['totale'], 2)
            else:
                intent.amount = tm.get_total()
        elif articolo['tipo'] == 'fattura':
            ms = CcpMovement.objects.filter(invoice=articolo['id'])
            amount = 0
            for m in ms:
                amount += m.unsolved()
            intent.payment_object_type = 'invoice'
            intent.payment_object_id = articolo['id']
            intent.amount = round(amount, 2)
            intent.school_year = anno.replace('_', '/')
        for item in channel_intents_data:
            setattr(intent, item['name'], item['value'])


        payment_intents.append(intent)

    return payment_intents


# Gestione dell'esito positivo di una transazione: crea i movimenti e i pagamenti corrispondenti
def handle_payment_succeeded(transaction_id, *args, **kwargs):
    payment_method_id = kwargs.get('payment_method', None)
    publish = kwargs.get('publish', False)
    lock = kwargs.get('lock', False)



    now = datetime.now().astimezone()
    now_date = now.strftime('%Y-%m-%d %H:%M:%S')

    with transaction.atomic():
        intents = CcpPaymentIntents.objects.select_for_update().filter(token=transaction_id, date_processed=None)
        ids = list(intents.values_list('id', flat=True))

        intents.update(date_processed=now_date)

        intents = CcpPaymentIntents.objects.filter(id__in=ids).all()

    # now_ts = int(time.mktime(now.timetuple())) # non dovrebbe servire
        payment_method = CcpPaymentMethod.objects.get(id=payment_method_id)


        for intent in intents:
            account = CoreBankAccount.objects.get(id=intent.bank_account_id)
            intent_date_ts = int(time.mktime(intent.date_created.timetuple()))
            if intent.payment_object_type == 'invoice':
                movements = CcpMovement.objects.get(invoice_id=intent.payment_object_id)
                for movement in movements:
                    # Salviamo sul movimento l'id e il token della transazione
                    movement.payment_intent_id = intent.id
                    movement.payment_intent_token = intent.token
                    m.save()
                    
                    # Creiamo il pagamento per il movimento
                    data = {
                        'amount': movement.unsolved(),
                        'operation_date': intent_date_ts,
                        'accountable_date': intent_date_ts,
                        'movement': movement,
                        'account': account,
                        'payer_id': intent.payer_id,
                        'payer_type': intent.payer_type,
                        'payer_surname': intent.payer_surname,
                        'payer_name': intent.payer_name,
                        'payer_fiscal_code': intent.payer_fiscal_code,
                        'payer_address': intent.payer_address,
                        'payer_city': intent.payer_city,
                        'payer_province': intent.payer_province,
                        'payer_zip_code': intent.payer_zip_code,
                        'payment_method': payment_method
                    }
                    try:
                        if create_payment(data):
                            if publish:
                                movement.date_published = now_date
                                movement.save()

                            intent.date_succeeded = now_date
                            intent.channel_key = ''
                            intent.satispay_private_key = ''

                            intent.save()
                    except:
                        pass
                    
                    
            else:
                if intent.payment_object_type == 'movement_type':
                    type = CcpType.objects.get(id=intent.payment_object_id)

                    data = {
                        'amount': intent.amount,
                        'school_year': intent.school_year,
                        'type': type,
                        'creation_date': intent_date_ts,
                        'expiration_date': intent_date_ts,
                        'subject_id': intent.subject_id,
                        'subject_data': intent.subject_data,
                        'subject_seat': None,
                        'subject_class': intent.subject_class,
                        'subject_type': 'S',
                        'subject_school_year': intent.school_year,
                        'subject_school_address_code': intent.subject_school_address_code,
                        'subject_school_address': intent.subject_school_address,
                        'description': type.name,
                        'locked': lock
                    }

                    m = create_movement(data)
                if intent.payment_object_type == 'movement':
                    m = CcpMovement.objects.get(id=intent.payment_object_id)

                m.payment_intent_id = intent.id
                m.payment_intent_token = intent.token

                m.save()

                data = {
                    'amount': intent.amount,
                    'operation_date': intent_date_ts,
                    'accountable_date': intent_date_ts,
                    'movement': m,
                    'account': account,
                    'payer_id': intent.payer_id,
                    'payer_type': intent.payer_type,
                    'payer_surname': intent.payer_surname,
                    'payer_name': intent.payer_name,
                    'payer_fiscal_code': intent.payer_fiscal_code,
                    'payer_address': intent.payer_address,
                    'payer_city': intent.payer_city,
                    'payer_province': intent.payer_province,
                    'payer_zip_code': intent.payer_zip_code,
                    'payment_method': payment_method
                }

                try:
                    if create_payment(data):
                        if publish:
                            m.date_published = now_date
                            m.save()

                        intent.date_succeeded = now_date
                        intent.channel_key = ''
                        intent.satispay_private_key = ''

                        intent.save()
                except:
                    pass


def handle_payment_canceled(transaction_id, *args, **kwargs):


        now = datetime.now().astimezone()
        now_date = now.strftime('%Y-%m-%d %H:%M:%S')

        with transaction.atomic():
            try:
                intents = CcpPaymentIntents.objects.select_for_update().filter(token=transaction_id, date_processed=None)
                ids = list(intents.values_list('id', flat=True))

                intents.update(date_processed=now_date)

                intents = CcpPaymentIntents.objects.filter(id__in=ids).all()

                for intent in intents:
                    intent.date_processed = now_date
                    intent.date_canceled = now_date
                    intent.channel_key = ''
                    intent.satispay_private_key = ''
                    intent.save()
            except:
                pass



def get_totale_intents(intents):
    totale = 0
    for intent in intents:
        totale += intent.amount

    return totale