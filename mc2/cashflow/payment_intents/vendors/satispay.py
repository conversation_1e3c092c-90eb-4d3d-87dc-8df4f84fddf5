import base64
import json

from mc2.cashflow.payment_intents.libraries.satispay import Satispay

class SatispayVendor():
    satispay = None
    env = None
    callback_url = None
    key_id = None
    private_key = None
    private_key_encoded = None
    name = 'SATISPAY'
    publish = False
    transaction_id = None
    payment_method_id = 31
    meta_items_max_length = 500
    lock = True
    def __init__(self, *args, **kwargs):
        env = kwargs.get('env', None)
        now = kwargs.get('now', None)
        self.payment_method_id = int(kwargs.get('payment_method_id', self.payment_method_id))
        self.publish = kwargs.get('publish', None)
        self.key_id = kwargs.get('key_id', None)
        self.mastercom_url = kwargs.get('mastercom_url', None)

        self.private_key_encoded = kwargs.get('private_key', None)
        self.private_key = base64.b64decode(self.private_key_encoded).decode('utf-8') if self.private_key_encoded else None

        self.lock = kwargs.get('lock', True)
        self.satispay = Satispay(env=env, now=now)
        self.satispay.set_key_id(self.key_id)
        self.satispay.set_private_key(self.private_key)
        self.satispay.set_callback_url(f"{self.mastercom_url}/mc_2/v1/satispay-payment-receiver/?transaction_id=" + '{uuid}')

    # Formats amount according to satispay requirements
    def get_total_amount(self, amount):
        return int(amount * 100)


    def get_token(self, amount, intents = None):
        if intents:
            payer_type=intents[0].payer_type
            payer_id=intents[0].payer_id
            subject_id = intents[0].subject_id
            items = []
            for intent in intents:
                type = 'T'
                if intent.payment_object_type == 'movement':
                    type = 'M'

                if intent.payment_object_type == 'invoice':
                    type = 'I'
                item = dict(
                    type=type,
                    id=intent.payment_object_id,
                    amount=intent.amount
                )
                items.append(item)
            items = json.dumps(items)
            items = items if len(items) < self.meta_items_max_length else 'item payload too long'
            metadata = {
                'payer_type': payer_type,
                'payer_id': payer_id,
                'subject_id': subject_id,
                'items': items
            }

        return self.satispay.create_payment(
            self.get_total_amount(amount), metadata=metadata
        )

    def get_publishable_key(self):
        return None

    def get_channel_intents_data(self):
        return [
            {'name': 'channel_key', 'value': self.key_id},
            {'name': 'satispay_private_key', 'value': self.private_key_encoded}
        ]

    def handle_webhook(self, request):
        pass


    # Returns the status attribute from
    def get_payment_status(self, payment_id):
        try:
            self.transaction_id = payment_id
            details = self.satispay.get_payment_details(payment_id)
            # print('details', details)
            return details.get('status', None)
        except Exception as e:
            return None
