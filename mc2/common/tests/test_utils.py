from django.test import TestCase

from ..utils import format_name

class UtilsTest(TestCase):

    def test_format_name(self):
        self.assertEqual(format_name(''), '')
        self.assertEqual(format_name('test'), 'Test')
        self.assertEqual(format_name('test test'), 'Test Test')
        self.assertEqual(format_name('TEST TEST'), 'Test Test')
        self.assertEqual(format_name('test test test'), 'Test Test Test')
        self.assertEqual(format_name('test test test test'), 'Test Test Test Test')
        self.assertEqual(format_name('test test test test test'), 'Test Test Test Test Test')