from django.test import TestCase

from .factories import ParameterFactory

class ParameterModelTest(TestCase):
    def test_parameter_str(self):
        parameter = ParameterFactory(name='Test Parameter')
        self.assertEqual(parameter.__str__(), 'Test Parameter')


    def test_parameter_value(self):
        parameter = ParameterFactory(name='Parameter Name', value='Parameter Value')
        self.assertEqual(parameter.value, 'Parameter Value')
        self.assertEqual(parameter.name, 'Parameter Name')

