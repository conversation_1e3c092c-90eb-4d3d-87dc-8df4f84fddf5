import factory

from ..models import Parameter, Contact, Cities, Regions


class ParameterFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Parameter

    name = factory.Sequence(lambda n: 'Parameter %s' % n)
    value = 'Value'


class RegionsFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Regions

    code = factory.Sequence(lambda n: '%02d' % (n % 100))
    name = factory.Sequence(lambda n: 'Region %s' % n)


class CitiesFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Cities

    description = factory.Sequence(lambda n: 'City %s' % n)
    city_code = factory.Sequence(lambda n: 'C%03d' % (n % 1000))
    province = factory.Sequence(lambda n: 'PR%02d' % (n % 100))
    region = factory.SubFactory(RegionsFactory)
    is_city = 1


class ContactFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Contact

    address = factory.Sequence(lambda n: 'Address %s' % n)
    phone_num = factory.Sequence(lambda n: '+39 123 456 %04d' % (n % 10000))
    fax = factory.Sequence(lambda n: '+39 123 456 %04d' % (n % 10000))
    city = factory.SubFactory(CitiesFactory)
    email = factory.Sequence(lambda n: '<EMAIL>' % n)
    mobile = factory.Sequence(lambda n: '+39 333 123 %04d' % (n % 10000))
    web = factory.Sequence(lambda n: 'https://www.example%s.com' % n)
    cap = factory.Sequence(lambda n: '%05d' % (n % 100000))

