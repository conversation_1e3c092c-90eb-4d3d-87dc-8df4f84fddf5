# Generated by Django 3.1.14 on 2025-09-05 09:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Regions',
            fields=[
                ('code', models.CharField(max_length=2, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
            ],
            options={
                'db_table': 'regions',
            },
        ),
        migrations.CreateModel(
            name='Cities',
            fields=[
                ('city_id', models.AutoField(primary_key=True, serialize=False)),
                ('description', models.CharField(max_length=50)),
                ('city_code', models.CharField(blank=True, max_length=10, null=True)),
                ('province', models.CharField(blank=True, max_length=5, null=True)),
                ('is_city', models.SmallIntegerField(blank=True, null=True)),
                ('region', models.ForeignKey(blank=True, db_column='region', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='common.regions')),
            ],
            options={
                'db_table': 'cities',
            },
        ),
        migrations.CreateModel(
            name='Parameter',
            fields=[
                ('parameter_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
                ('value', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'parameter',
            },
        ),
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('contact_id', models.AutoField(primary_key=True, serialize=False)),
                ('address', models.TextField(blank=True, null=True)),
                ('phone_num', models.TextField(blank=True, null=True)),
                ('fax', models.TextField(blank=True, null=True)),
                ('email', models.TextField(blank=True, null=True)),
                ('mobile', models.TextField(blank=True, null=True)),
                ('web', models.TextField(blank=True, null=True)),
                ('cap', models.CharField(blank=True, max_length=5, null=True)),
                ('city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='common.cities')),
            ],
            options={
                'db_table': 'contact',
            },
        ),
    ]
