from django.test import TestCase

from ..utils import get_institute
from mc2.institute.models import Institute
from mc2.institute.tests.factories import InstituteFactory

class UtilsTest(TestCase):

    def test_get_current_institute_no_institute(self):
        self.assertIsNone(get_institute())

    def test_get_current_institute_ok(self):
        InstituteFactory(def_field=True)
        self.assertIsInstance(get_institute(), Institute)