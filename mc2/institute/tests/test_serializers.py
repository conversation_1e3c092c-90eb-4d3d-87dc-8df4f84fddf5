from django.test import TestCase
from rest_framework.test import APIRequestFactory

from mc2.common.tests.factories import ContactFactory, CitiesFactory
from mc2.institute.tests.factories import InstituteFactory
from mc2.institute.serializers import InstituteSerializer
from mc2.institute.models import Institute


class InstituteSerializerTest(TestCase):

    def setUp(self):
        self.factory = APIRequestFactory()
        self.city = CitiesFactory()
        self.contact = ContactFactory(city=self.city)
        self.institute = InstituteFactory(contact=self.contact)

    def test_serializer_contains_expected_fields(self):
        """Test that serializer contains all expected fields"""
        serializer = InstituteSerializer(instance=self.institute)
        data = serializer.data

        expected_fields = [
            "institute_id", "name", "mechan_code", "fiscal_code",
            "school_fiscal_code", "school_type", "postal_account",
            "job_director_id", "job_vice_director_id", "job_dsga_id",
            "job_personnel_id", "job_accounting_id", "job_warehouse_id",
            "job_registry_id", "contact_id", "ipa_code", "address",
            "phone_num", "fax", "email", "mobile", "web", "city_id",
            "cap", "ade_email", "city_code", "province", "region",
            "is_city", "def_field"
        ]

        for field in expected_fields:
            self.assertIn(field, data)

    def test_serializer_with_valid_data(self):
        """Test serializer with valid institute data"""
        serializer = InstituteSerializer(instance=self.institute)
        data = serializer.data

        self.assertEqual(data['name'], self.institute.name)
        self.assertEqual(data['fiscal_code'], self.institute.fiscal_code)
        self.assertEqual(data['school_fiscal_code'], self.institute.school_fiscal_code)
        self.assertEqual(data['def_field'], self.institute.def_field)

    def test_serializer_with_contact_data(self):
        """Test serializer includes contact-related fields"""
        serializer = InstituteSerializer(instance=self.institute)
        data = serializer.data

        # Contact fields should be included
        self.assertEqual(data['contact_id'], self.contact.contact_id)
        self.assertEqual(data['address'], self.contact.address)
        self.assertEqual(data['phone_num'], self.contact.phone_num)
        self.assertEqual(data['email'], self.contact.email)
        self.assertEqual(data['city_id'], self.contact.city.city_id)

    def test_serializer_without_contact(self):
        """Test serializer with institute that has no contact"""
        institute_no_contact = InstituteFactory(contact=None)
        serializer = InstituteSerializer(instance=institute_no_contact)
        data = serializer.data

        self.assertIsNone(data['contact_id'])
        self.assertIsNone(data['address'])
        self.assertIsNone(data['phone_num'])
        self.assertIsNone(data['email'])

    def test_serializer_create_valid_institute(self):
        """Test creating a new institute through serializer"""
        valid_data = {
            'name': 'New Test Institute',
            'mechan_code': 'TEST001',
            'fiscal_code': '98765432109',
            'school_fiscal_code': '98765432108',
            'def_field': False,
            'dir_birth': 1,
            'dir_emp_id': 1,
            'adir_emp_id': 1,
            'presge_emp_id': 1,
            'segcons_emp_id': 1,
            'prescon_emp_id': 1,
            'respacq_emp_id': 1,
        }

        serializer = InstituteSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())

        institute = serializer.save()
        self.assertIsInstance(institute, Institute)
        self.assertEqual(institute.name, 'New Test Institute')
        self.assertEqual(institute.fiscal_code, '98765432109')

    def test_serializer_update_institute(self):
        """Test updating an existing institute through serializer"""
        update_data = {
            'name': 'Updated Institute Name',
            'fiscal_code': '***********',
        }

        serializer = InstituteSerializer(instance=self.institute, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())

        updated_institute = serializer.save()
        self.assertEqual(updated_institute.name, 'Updated Institute Name')
        self.assertEqual(updated_institute.fiscal_code, '***********')

    def test_serializer_with_all_job_fields(self):
        """Test serializer with all job-related fields populated"""
        institute_with_jobs = InstituteFactory(
            job_director_id=100,
            job_vice_director_id=101,
            job_dsga_id=102,
            job_personnel_id=103,
            job_accounting_id=104,
            job_warehouse_id=105,
            job_registry_id=106
        )

        serializer = InstituteSerializer(instance=institute_with_jobs)
        data = serializer.data

        self.assertEqual(data['job_director_id'], 100)
        self.assertEqual(data['job_vice_director_id'], 101)
        self.assertEqual(data['job_dsga_id'], 102)
        self.assertEqual(data['job_personnel_id'], 103)
        self.assertEqual(data['job_accounting_id'], 104)
        self.assertEqual(data['job_warehouse_id'], 105)
        self.assertEqual(data['job_registry_id'], 106)

    def test_serializer_with_optional_fields(self):
        """Test serializer with optional fields populated"""
        institute_with_optional = InstituteFactory(
            school_type='SEC',
            postal_account=*********,
            ipa_code='TEST_IPA',
            ade_email='<EMAIL>'
        )

        serializer = InstituteSerializer(instance=institute_with_optional)
        data = serializer.data

        self.assertEqual(data['school_type'], 'SEC')
        self.assertEqual(data['postal_account'], *********)
        self.assertEqual(data['ipa_code'], 'TEST_IPA')
        self.assertEqual(data['ade_email'], '<EMAIL>')