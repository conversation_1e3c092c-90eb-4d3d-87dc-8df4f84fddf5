from django.db import models

from mc2.common.models import Contact


class Institute(models.Model):
    institute_id = models.AutoField(primary_key=True)
    name = models.Char<PERSON>ield(max_length=200)
    mechan_code = models.Char<PERSON>ield(max_length=16)
    contact = models.OneToOneField(Contact, models.DO_NOTHING, blank=True, null=True)
    fiscal_code = models.CharField(max_length=16, blank=True, null=True)
    school_type = models.Char<PERSON>ield(max_length=3, blank=True, null=True)
    parent = models.IntegerField(blank=True, null=True)
    def_field = models.BooleanField(db_column='def', blank=True, null=True)  # Field renamed because it was a Python reserved word.
    dir_name = models.Char<PERSON><PERSON>(max_length=100, blank=True, null=True)
    dir_surname = models.Char<PERSON><PERSON>(max_length=100, blank=True, null=True)
    adir_name = models.Cha<PERSON><PERSON><PERSON>(max_length=100, blank=True, null=True)
    adir_surname = models.<PERSON><PERSON><PERSON><PERSON>(max_length=100, blank=True, null=True)
    pres_ge_name = models.Char<PERSON><PERSON>(max_length=100, blank=True, null=True)
    pres_ge_surname = models.Char<PERSON><PERSON>(max_length=100, blank=True, null=True)
    seg_cons_name = models.<PERSON>r<PERSON>ield(max_length=100, blank=True, null=True)
    seg_cons_surname = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    pres_con_name = models.CharField(max_length=100, blank=True, null=True)
    pres_con_surname = models.CharField(max_length=100, blank=True, null=True)
    dir_fiscal_code = models.TextField(blank=True, null=True)
    school_fiscal_code = models.TextField(blank=True, null=True)
    inpdap_code = models.TextField(blank=True, null=True)
    assicurazioni_sanitarie = models.TextField(blank=True, null=True)
    dir_sesso = models.CharField(max_length=1)
    dir_birth = models.BigIntegerField()
    dir_city = models.TextField()
    postal_account = models.BigIntegerField(blank=True, null=True)
    ateco_code = models.TextField(blank=True, null=True)
    activity_code = models.TextField(blank=True, null=True)
    dir_curr_addr = models.TextField()
    dir_curr_city = models.TextField()
    dir_curr_phone = models.TextField()
    dir_emp_id = models.IntegerField() # per non stare ad importare Employee. Originale => models.ForeignKey(Employee, models.DO_NOTHING, blank=True, null=True)
    adir_emp_id = models.IntegerField()
    presge_emp_id = models.IntegerField()
    segcons_emp_id = models.IntegerField()
    prescon_emp_id = models.IntegerField()
    respacq_emp_id = models.IntegerField()
    job_director_id = models.IntegerField(blank=True, null=True)
    job_vice_director_id = models.IntegerField(blank=True, null=True)
    job_dsga_id = models.IntegerField(blank=True, null=True)
    job_personnel_id = models.IntegerField(blank=True, null=True)
    job_accounting_id = models.IntegerField(blank=True, null=True)
    job_warehouse_id = models.IntegerField(blank=True, null=True)
    job_registry_id = models.IntegerField(blank=True, null=True)
    ipa_code = models.CharField(max_length=255, blank=True, null=True)
    ae_fiscal_code = models.CharField(max_length=16, blank=True, null=True)
    ade_email = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'institute'

    def __str__(self):
        return self.name
