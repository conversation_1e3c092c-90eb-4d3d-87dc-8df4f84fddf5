# Generated by Django 3.1.14 on 2025-09-05 09:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Institute',
            fields=[
                ('institute_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=200)),
                ('mechan_code', models.Char<PERSON><PERSON>(max_length=16)),
                ('fiscal_code', models.Char<PERSON>ield(blank=True, max_length=16, null=True)),
                ('school_type', models.Char<PERSON>ield(blank=True, max_length=3, null=True)),
                ('parent', models.Integer<PERSON>ield(blank=True, null=True)),
                ('def_field', models.BooleanField(blank=True, db_column='def', null=True)),
                ('dir_name', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('dir_surname', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('adir_name', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('adir_surname', models.<PERSON>r<PERSON>ield(blank=True, max_length=100, null=True)),
                ('pres_ge_name', models.CharField(blank=True, max_length=100, null=True)),
                ('pres_ge_surname', models.<PERSON>r<PERSON>ield(blank=True, max_length=100, null=True)),
                ('seg_cons_name', models.CharField(blank=True, max_length=100, null=True)),
                ('seg_cons_surname', models.CharField(blank=True, max_length=100, null=True)),
                ('pres_con_name', models.CharField(blank=True, max_length=100, null=True)),
                ('pres_con_surname', models.CharField(blank=True, max_length=100, null=True)),
                ('dir_fiscal_code', models.TextField(blank=True, null=True)),
                ('school_fiscal_code', models.TextField(blank=True, null=True)),
                ('inpdap_code', models.TextField(blank=True, null=True)),
                ('assicurazioni_sanitarie', models.TextField(blank=True, null=True)),
                ('dir_sesso', models.CharField(max_length=1)),
                ('dir_birth', models.BigIntegerField()),
                ('dir_city', models.TextField()),
                ('postal_account', models.BigIntegerField(blank=True, null=True)),
                ('ateco_code', models.TextField(blank=True, null=True)),
                ('activity_code', models.TextField(blank=True, null=True)),
                ('dir_curr_addr', models.TextField()),
                ('dir_curr_city', models.TextField()),
                ('dir_curr_phone', models.TextField()),
                ('dir_emp_id', models.IntegerField()),
                ('adir_emp_id', models.IntegerField()),
                ('presge_emp_id', models.IntegerField()),
                ('segcons_emp_id', models.IntegerField()),
                ('prescon_emp_id', models.IntegerField()),
                ('respacq_emp_id', models.IntegerField()),
                ('job_director_id', models.IntegerField(blank=True, null=True)),
                ('job_vice_director_id', models.IntegerField(blank=True, null=True)),
                ('job_dsga_id', models.IntegerField(blank=True, null=True)),
                ('job_personnel_id', models.IntegerField(blank=True, null=True)),
                ('job_accounting_id', models.IntegerField(blank=True, null=True)),
                ('job_warehouse_id', models.IntegerField(blank=True, null=True)),
                ('job_registry_id', models.IntegerField(blank=True, null=True)),
                ('ipa_code', models.CharField(blank=True, max_length=255, null=True)),
                ('ae_fiscal_code', models.CharField(blank=True, max_length=16, null=True)),
                ('ade_email', models.CharField(blank=True, max_length=255, null=True)),
                ('contact', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='common.contact')),
            ],
            options={
                'db_table': 'institute',
            },
        ),
    ]
