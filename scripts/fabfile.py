import json
from fabric import task, Config
from fabric import ThreadingGroup, SerialGroup
from fabric.connection import Connection
from fabric.exceptions import GroupException

# https://gestionale.registroelettronico.com/api/networking/ansible/inventory/?format=json
json_file_path='schools.json'
with open(json_file_path, 'r') as file:
    data = json.load(file)

hostsJson = data['mastercom-servers-active']['hosts']
tasks = []
i=0
hosts = []
hostRef = {}
for host in hostsJson:
    sshHost = data['_meta']['hostvars'][host]['ansible_ssh_host']
    sshPort = data['_meta']['hostvars'][host]['ansible_ssh_port'] if 'ansible_ssh_port' in data['_meta']['hostvars'][host].keys() else False
    dbPort = data['_meta']['hostvars'][host]['mastercom_master_db_local_port']
    sshHost = f"{sshHost}:{sshPort}" if sshPort else sshHost
    hosts.append(sshHost)
    hostRef[sshHost] = host
    i+=1





pool = ThreadingGroup(*hosts, connect_timeout=10)


def print_data(results):
    fo = open('./output.txt', 'w')
    for conn, result in results.items():
        host = f"{conn.host}:{conn.port}" if conn.port != 22 else conn.host
        fo.write(f"{hostRef[host]}: {result}")
    fo.close()

@task
def check_invoices_movements_links(c):
    query = "SELECT count(distinct ccp_invoice.id) FROM ccp_invoice left join ccp_movement on (invoice_id=ccp_invoice.id) where ccp_movement.id is null and ccp_invoice.number>0;"
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)

@task
def update_mc2_to_use_couch_authentication(c):
    query = "update parameter  set value = 't' WHERE name = 'COUCH_AUTHENTICATION';"
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)




@task
def check_invoices_bad_credit_note(c):
    query = "select count(*) from ccp_invoice where credit_note = 't' and (select incoming from ccp_view_movement where ccp_view_movement.id = (ccp_invoice.rows::json->0->>'id')::integer)=true and date>='2023-09-01';"
    #query = "select date from ccp_invoice where credit_note = 't' and (select incoming from ccp_view_movement where ccp_view_movement.id = (ccp_invoice.rows::json->0->>'id')::integer)=true order by date desc limit 1"
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)

@task
def check_login_from_2025(c):
    query = "select count(*) from activity where message ilike 'login' and app_name ilike '%mc2%' and datetime>='2025-05-01'"
    results = None
    try :
        results = pool.sudo(f'psql logger -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)



@task
def check_area_ccp(c):
    query = "SELECT value FROM parameter WHERE name = 'AREA_CCP';"
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)

@task
def exec_prometheus(c):
    results = None
    try :
        results = pool.sudo('php /var/www/mc2-api/index.php prometheus',  hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)


@task
def check_movement_invoice_one_level_fix(c):
    query = "SELECT * from (select cm.id as mid, array_agg(ci.id) as cid,  count(ci.id) as count from ccp_movement cm left join ccp_invoice ci on ci.rows like '%'||cm.id::text||'%' and ci.number>0 and cm.invoice_id is null group by cm.id ) t where count>0;"
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)

@task
def update_repo(c):
    results = None
    try :
        results = pool.sudo('apt-get update', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)


@task
def install_mc2(c):
    results = None
    try :
        results = pool.sudo('sudo apt-get update && sudo apt-get install -y master-mc2 master-mc2-api', hide='stdout')
        #results = pool.sudo('apt-get install -y master-mc2-old', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)


@task
def check_payment_zero_amount(c):
    query = "select count(*) from ccp_view_movement where total_additionals=amount*-1 and total_additionals!=0 and linked_payments!='';"
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)

@task
def check_protocol_counter(c):
    query = "SELECT (select count(*) from protocol_protocol where date>=1735686000) AS anno_corrente, (select count(*) from protocol_protocol) AS tutti;" # 2025-01-01 00:00:00
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)

@task
def exec_merge_users_mc_mc2(c):
    try:
        # results = pool.put('/home/<USER>/workspace/projects/mc2-api/scripts/merge_user_mc_mc2.php', '/tmp/merge_user_mc_mc2.php')
        results = pool.sudo('php /tmp/merge_user_mc_mc2.php', hide='stdout')
    except Exception as e:
        results = e.result
    # get script results in std out
    print_data(results)

@task
def exec_mc2_file_disk_usage(c):
    try:
        results = pool.sudo(f'du -sh /var/files/mc2/|cut -f1', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)


@task
def exec_messenger_file_disk_usage(c):
    #query = "select sum(size)/1000000 as size from (select distinct owner, name, size from files where parent_id is null and hidden=false) t;";
    #query = "select year, sum(size)/1000000 as size, 'MB' as unit from (select distinct on ( owner, name, size) size, properties->'year' as year from files where parent_id is null and hidden=false) t group by year order by year;";
    # ATTACHMENT
    query = "select sum(size)/1000000 as size from files where parent_id is not null and hidden=false";
    query = "select properties->'year'  as year, sum(size)/1000000 as size, 'MB' as unit from files where parent_id is not null and hidden=false group by properties->'year'  order by properties->'year' ";
    results = None
    try :
        results = pool.sudo(f'psql messenger -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)


@task
def exec_get_tutti_ccp_type(c):
    #query = "select sum(size)/1000000 as size from (select distinct owner, name, size from files where parent_id is null and hidden=false) t;";
    #query = "select year, sum(size)/1000000 as size, 'MB' as unit from (select distinct on ( owner, name, size) size, properties->'year' as year from files where parent_id is null and hidden=false) t group by year order by year;";
    # ATTACHMENT
    query = "SELECT count(*) from ccp_type WHERE school_year = 'TUTTI'";
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)


@task
def exec_get_count_marketplace_type_id(c):
    query = "select count(distinct(id_tipo_movimento)) from marketplace where id_tipo_movimento>0 and flag_canc=0;";
    results = None
    try :
        results = pool.sudo(f'psql mastercom_2024_2025 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)

@task
def check_stripe(c):
    query = "SELECT  value FROM parameter WHERE name ilike 'STRIPE_PUBLIC_KEY' AND value != '';";
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)




@task
def check_archive_models(c):
    query = "select count(*) from archive_document where to_timestamp(upload_date)>='2024-01-01'";
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)

@task
def check_nexus_mc2(c):
    # query = "select user_name, couch_id from users where couch_id is not null and couch_id!='-';";
    query = "select value from parameter where name ilike 'COUCH_AUTHENTICATION' and value='f';";

    try:
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result

    print_data(results)

@task
def check_mail_account(c):
    query = "select * from archive_mail_account where active=true;"
    results = None
    try :
        results = pool.sudo(f'psql mastercom2 -c "{query}"', user='postgres', hide='stdout')
    except Exception as e:
        results = e.result


    print_data(results)

# TODO
# Crediti senza metodo pagamento

"""
SELECT * from (
select cm.id as mid, array_agg(ci.id) as cid,  count(ci.id) as count from ccp_movement cm left join ccp_invoice ci on ci.rows::json->0->>'id'=cm.id::text and ci.number>0 and cm.invoice_id is null group by cm.id order by 3 desc
) t where count>0;


SELECT * from (
select cm.id as mid, array_agg(ci.id) as cid,  count(ci.id) as count from ccp_movement cm left join ccp_invoice ci on ci.rows like '"id": '||cm.id::text||'%' and ci.number>0 and cm.invoice_id is null group by cm.id
) t where count>0;
"""
