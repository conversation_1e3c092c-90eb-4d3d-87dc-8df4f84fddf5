from .default import *
import os

def getenv_bool(name, default=False):
    value = os.getenv(name)
    if value is None:
        return default
    if value.lower() in ('yes', 'true'):
        return True
    return False


DEBUG = getenv_bool('DEBUG', default=False)


ALLOWED_HOSTS = ['*']

MASTERCOM_URL = os.getenv('MASTERCOM_URL')
NEXT_API_USER = os.getenv('NEXT_API_USER')
NEXT_API_PASSWORD = os.getenv('NEXT_API_PASSWORD')
NEXT_API_URL = os.getenv('NEXT_API_URL')

APP_VERSION = '0.0.34'

INSTALLED_APPS = [
    'mc2.common',
    'mc2.institute',
    'mc2.cashflow',
    'mc2.cashflow.payment_intents',
    'mc2.cashflow.exports',
    'rest_framework',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

DATABASES = {
    'default': {
        "ENGINE": "django.db.backends.postgresql",
        'NAME': os.getenv('POSTGRES_DB', 'postgres'),
        'USER': os.getenv('POSTGRES_USER', 'postgres'),
        'PASSWORD': os.getenv('POSTGRES_PASSWORD', 'postgres'),
        'HOST': os.getenv('POSTGRES_HOST', 'db'),
        'PORT': os.getenv('POSTGRES_PORT', '5432'),
    },
}

# Permette di autenticare le chiamate API senza bisogno di un token di autenticazione
AUTH_ALLOWED_HOSTS = ['127.0.0.1', 'localhost', ]

# Logging
LOGGING = dict(
    version=1,
    disable_existing_loggers=False,
    root=dict(
        handlers=['console'],
        level=os.environ.get('LOG_LEVEL', 'INFO'),
    ),
    handlers=dict(
        console={
            'class': 'logging.StreamHandler',
            'formatter': 'default',
            'stream': 'ext://sys.stdout',
        },
    ),
    formatters=dict(
        default={
            'class': 'logging.Formatter',
            'format': '%(levelname)-8s %(name)s %(message)s',
        },
    ),
)

